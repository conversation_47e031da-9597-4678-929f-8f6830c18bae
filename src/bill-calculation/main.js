const path = require("path");
const fs = require("fs");
const {
  inputDir,
  outputDir,
  showWelcomeMessage,
  ensureDirectories,
} = require("./config");
const { parseExcelFile, getExcelFiles } = require("./excel-reader");
const { updateCostCalculationSheet } = require("./cost-calculator");
const { generateMaterialList } = require("./material-generator");
const { generateLaborList } = require("./labor-generator");
const { generateSubcontractList } = require("./subcontract-generator");
const { generateExcelOutput } = require("./excel-writer");
const { loadAdditionalFeeFromWorkbook } = require("./additional-fee-reader");
const { waitForUserExit } = require("./utils");

/**
 * 清单测算系统主程序
 * 集成所有模块，提供完整的端到端功能
 */

/**
 * 处理单个Excel文件
 * @param {string} filePath - Excel文件路径
 * @returns {Promise<Object>} 处理结果
 */
async function processSingleFile(filePath) {
  const fileName = path.basename(filePath);
  console.log(
    `\n\x1b[36m==================================================\x1b[0m`
  );
  console.log(`\x1b[32m           开始处理文件: ${fileName}\x1b[0m`);
  console.log(
    `\x1b[36m==================================================\x1b[0m`
  );

  try {
    // 1. 解析Excel文件
    console.log(`\x1b[36m[步骤1] \x1b[0m解析Excel文件...`);
    const parseResult = await parseExcelFile(filePath);

    if (!parseResult.success) {
      throw new Error(`Excel解析失败: ${parseResult.error}`);
    }

    console.log(
      `  \x1b[32m✓ \x1b[0m成功解析 ${parseResult.userInputData.length} 个项目`
    );

    // 1.5. 读取附加费数据并更新配置
    console.log(`\x1b[36m[步骤1.5] \x1b[0m读取附加费数据...`);
    const additionalFees = loadAdditionalFeeFromWorkbook(parseResult.workbook);
    console.log(`  \x1b[32m✓ \x1b[0m附加费配置已更新`);

    // 2. 更新成本测算表
    console.log(`\x1b[36m[步骤2] \x1b[0m更新分部分项成本测算...`);
    const costResult = updateCostCalculationSheet(parseResult.userInputData);

    if (!costResult.success) {
      throw new Error(`成本测算更新失败: ${costResult.error}`);
    }

    console.log(
      `  \x1b[32m✓ \x1b[0m成功更新 ${costResult.calculatedResults.length} 个项目的成本测算`
    );

    // 3. 生成材料清单
    console.log(`\x1b[36m[步骤3] \x1b[0m生成材料清单...`);
    const materialResult = generateMaterialList(costResult.calculatedResults, {
      concreteBasePrice: 300,
      applyConcretePrice: true,
    });

    if (!materialResult.success) {
      console.log(
        `  \x1b[33m⚠ \x1b[0m材料清单生成失败: ${materialResult.error}`
      );
    } else {
      console.log(
        `  \x1b[32m✓ \x1b[0m成功生成 ${materialResult.materialList.length} 个材料清单项`
      );
    }

    // 4. 生成劳务清单
    console.log(`\x1b[36m[步骤4] \x1b[0m生成劳务清单...`);
    const laborResult = generateLaborList(costResult.calculatedResults);

    if (!laborResult.success) {
      console.log(`  \x1b[33m⚠ \x1b[0m劳务清单生成失败: ${laborResult.error}`);
    } else {
      console.log(
        `  \x1b[32m✓ \x1b[0m成功生成 ${laborResult.laborList.length} 个劳务清单项`
      );
    }

    // 5. 生成专业分包清单
    console.log(`\x1b[36m[步骤5] \x1b[0m生成专业分包清单...`);
    const subcontractResult = generateSubcontractList(
      costResult.calculatedResults
    );

    if (!subcontractResult.success) {
      console.log(
        `  \x1b[33m⚠ \x1b[0m专业分包清单生成失败: ${subcontractResult.error}`
      );
    } else {
      console.log(
        `  \x1b[32m✓ \x1b[0m成功生成 ${subcontractResult.subcontractList.length} 个专业分包清单项`
      );
    }

    // 6. 生成输出Excel文件
    console.log(`\x1b[36m[步骤6] \x1b[0m生成输出Excel文件...`);

    const inputData = {
      worksheet: parseResult.worksheet,
      userInputData: parseResult.userInputData,
    };

    const allResults = {
      calculatedData: costResult.calculatedResults,
      materialList: materialResult.success ? materialResult.materialList : [],
      laborList: laborResult.success ? laborResult.laborList : [],
      subcontractList: subcontractResult.success
        ? subcontractResult.subcontractList
        : [],
    };

    // 生成输出文件名
    const baseName = path.basename(fileName, path.extname(fileName));
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, "-");
    const outputFileName = `${baseName}_测算结果_${timestamp}.xlsx`;
    const outputPath = path.join(outputDir, outputFileName);

    const outputResult = await generateExcelOutput(
      inputData,
      allResults,
      outputPath
    );

    if (!outputResult.success) {
      throw new Error(`Excel输出失败: ${outputResult.error}`);
    }

    console.log(`  \x1b[32m✓ \x1b[0m成功生成输出文件: ${outputFileName}`);

    // 7. 生成处理报告
    const report = generateProcessingReport(fileName, {
      parseResult,
      costResult,
      materialResult,
      laborResult,
      subcontractResult,
      outputResult,
    });

    console.log(
      `\x1b[36m==================================================\x1b[0m`
    );
    console.log(`\x1b[32m           文件处理完成: ${fileName}\x1b[0m`);
    console.log(
      `\x1b[36m==================================================\x1b[0m`
    );

    return {
      success: true,
      fileName,
      outputPath: outputResult.outputPath,
      report,
    };
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m处理文件失败: ${error.message}`);
    console.log(
      `\x1b[36m==================================================\x1b[0m`
    );
    console.log(`\x1b[31m           文件处理失败: ${fileName}\x1b[0m`);
    console.log(
      `\x1b[36m==================================================\x1b[0m`
    );

    return {
      success: false,
      fileName,
      error: error.message,
    };
  }
}

/**
 * 生成处理报告
 * @param {string} fileName - 文件名
 * @param {Object} results - 处理结果
 * @returns {Object} 处理报告
 */
function generateProcessingReport(fileName, results) {
  const {
    parseResult,
    costResult,
    materialResult,
    laborResult,
    subcontractResult,
    outputResult,
  } = results;

  const report = {
    fileName,
    processTime: new Date(),
    inputStatistics: parseResult.statistics,
    outputStatistics: {
      costCalculationItems: costResult.calculatedResults.length,
      materialListItems: materialResult.success
        ? materialResult.materialList.length
        : 0,
      laborListItems: laborResult.success ? laborResult.laborList.length : 0,
      subcontractListItems: subcontractResult.success
        ? subcontractResult.subcontractList.length
        : 0,
      worksheetCount: outputResult.worksheetCount,
    },
    summary: {
      totalInputItems: parseResult.userInputData.length,
      totalOutputItems: costResult.calculatedResults.length,
      materialAmount: materialResult.success
        ? materialResult.summary.totalAmount
        : 0,
      laborAmount: laborResult.success ? laborResult.summary.totalAmount : 0,
      subcontractAmount: subcontractResult.success
        ? subcontractResult.summary.totalAmount
        : 0,
    },
  };

  return report;
}

/**
 * 处理所有Excel文件
 * @returns {Promise<Object>} 处理结果汇总
 */
async function processAllFiles() {
  console.log(`\x1b[36m[扫描] \x1b[0m扫描输入目录: \x1b[32m${inputDir}\x1b[0m`);

  // 获取所有Excel文件
  const excelFiles = getExcelFiles(inputDir);

  if (excelFiles.length === 0) {
    console.log(`\x1b[33m[提示] \x1b[0m输入目录中没有找到Excel文件`);
    console.log(`\x1b[33m[提示] \x1b[0m请将Excel文件放入: ${inputDir}`);
    return {
      success: false,
      message: "没有找到Excel文件",
      results: [],
    };
  }

  console.log(
    `\x1b[36m[发现] \x1b[0m找到 \x1b[32m${excelFiles.length}\x1b[0m 个Excel文件`
  );

  const results = [];
  let successCount = 0;
  let failureCount = 0;

  // 逐个处理文件
  for (let i = 0; i < excelFiles.length; i++) {
    const filePath = excelFiles[i];

    console.log(
      `\n\x1b[36m[进度] \x1b[0m(${i + 1}/${excelFiles.length}) 处理文件...`
    );

    const result = await processSingleFile(filePath);
    results.push(result);

    if (result.success) {
      successCount++;
    } else {
      failureCount++;
    }
  }

  // 显示汇总结果
  console.log(
    `\n\x1b[36m==================================================\x1b[0m`
  );
  console.log(`\x1b[32m           批量处理完成\x1b[0m`);
  console.log(
    `\x1b[36m==================================================\x1b[0m`
  );
  console.log(
    `\x1b[36m[汇总] \x1b[0m总计: \x1b[32m${excelFiles.length}\x1b[0m 个文件`
  );
  console.log(
    `\x1b[36m[汇总] \x1b[0m成功: \x1b[32m${successCount}\x1b[0m 个文件`
  );
  console.log(
    `\x1b[36m[汇总] \x1b[0m失败: \x1b[31m${failureCount}\x1b[0m 个文件`
  );

  if (successCount > 0) {
    console.log(
      `\x1b[36m[输出] \x1b[0m结果文件保存在: \x1b[32m${outputDir}\x1b[0m`
    );
  }

  return {
    success: successCount > 0,
    totalFiles: excelFiles.length,
    successCount,
    failureCount,
    results,
  };
}

/**
 * 主程序入口
 */
async function main() {
  try {
    // 显示欢迎信息
    showWelcomeMessage();

    // 确保目录存在
    ensureDirectories();

    // 处理所有文件
    const result = await processAllFiles();

    if (result.success) {
      console.log(
        `\n\x1b[32m🎉 处理完成！共成功处理 ${result.successCount} 个文件。\x1b[0m`
      );
    } else {
      console.log(`\n\x1b[31m❌ 处理失败！请检查输入文件和错误信息。\x1b[0m`);
    }
  } catch (error) {
    console.error(`\n\x1b[31m[系统错误] \x1b[0m${error.message}`);
    console.error(`\x1b[31m[错误堆栈] \x1b[0m${error.stack}`);
  } finally {
    // 等待用户输入后退出
    waitForUserExit();
  }
}

// 如果直接运行此文件，则执行主程序
if (require.main === module) {
  main();
}

module.exports = {
  processSingleFile,
  processAllFiles,
  generateProcessingReport,
  main,
};
