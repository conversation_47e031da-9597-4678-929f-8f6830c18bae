const ExcelJS = require('exceljs');
const { BusinessRules } = require('./config');

/**
 * 附加费数据读取器
 * 从Excel文件中读取4-附加费页签的数据，并更新BusinessRules配置
 */

/**
 * 从Excel工作簿中读取附加费数据
 * @param {ExcelJS.Workbook} workbook - Excel工作簿对象
 * @returns {Object} 附加费数据对象
 */
function readAdditionalFeeData(workbook) {
  console.log(`\n\x1b[36m[附加费读取] \x1b[0m开始读取附加费数据`);
  
  try {
    const additionalFeeSheet = workbook.getWorksheet('4-附加费');
    
    if (!additionalFeeSheet) {
      console.log(`  \x1b[33m[警告] \x1b[0m未找到4-附加费页签，使用默认附加费配置`);
      return BusinessRules.concretePricing.additives;
    }
    
    const additionalFees = {};
    let processedCount = 0;
    
    // 从第2行开始读取数据（第1行是表头）
    for (let rowNumber = 2; rowNumber <= additionalFeeSheet.rowCount; rowNumber++) {
      const row = additionalFeeSheet.getRow(rowNumber);
      
      // 读取各列数据
      const serialNo = getCellValue(row.getCell(1)); // A列：序号
      const feeName = getCellValue(row.getCell(2));   // B列：附加费名称
      const unit = getCellValue(row.getCell(3));      // C列：单位
      const feeAmount = getCellValue(row.getCell(4)); // D列：费用金额
      
      // 跳过空行或无效数据
      if (!feeName || feeName.trim() === '') {
        continue;
      }
      
      // 如果有费用金额，则添加到附加费字典
      if (feeAmount && !isNaN(parseFloat(feeAmount))) {
        const cleanFeeName = feeName.trim();
        const amount = parseFloat(feeAmount);
        
        additionalFees[cleanFeeName] = amount;
        processedCount++;
        
        console.log(`  \x1b[32m[读取] \x1b[0m${cleanFeeName}: ${amount}元/立方米`);
      } else {
        console.log(`  \x1b[33m[跳过] \x1b[0m${feeName}: 无费用金额`);
      }
    }
    
    console.log(`  \x1b[36m[附加费读取] \x1b[0m完成，共读取 \x1b[32m${processedCount}\x1b[0m 个附加费项目`);
    
    return additionalFees;
    
  } catch (error) {
    console.error(`  \x1b[31m[错误] \x1b[0m读取附加费数据失败: ${error.message}`);
    console.log(`  \x1b[33m[回退] \x1b[0m使用默认附加费配置`);
    return BusinessRules.concretePricing.additives;
  }
}

/**
 * 获取单元格值，处理各种数据类型
 * @param {ExcelJS.Cell} cell - Excel单元格对象
 * @returns {string|number|null} 单元格值
 */
function getCellValue(cell) {
  if (!cell || cell.value === null || cell.value === undefined) {
    return null;
  }
  
  // 处理富文本
  if (typeof cell.value === 'object' && cell.value.richText) {
    return cell.value.richText.map(rt => rt.text).join('');
  }
  
  // 处理公式
  if (typeof cell.value === 'object' && cell.value.formula) {
    return cell.value.result || cell.value.formula;
  }
  
  return cell.value;
}

/**
 * 更新BusinessRules中的附加费配置
 * @param {Object} additionalFees - 从Excel读取的附加费数据
 */
function updateAdditionalFeeConfig(additionalFees) {
  console.log(`\n\x1b[36m[配置更新] \x1b[0m更新附加费配置`);
  
  // 备份原始配置
  const originalAdditives = { ...BusinessRules.concretePricing.additives };
  
  // 更新配置
  BusinessRules.concretePricing.additives = { ...additionalFees };
  
  console.log(`  \x1b[32m[更新] \x1b[0m附加费配置已更新`);
  console.log(`  \x1b[36m[对比] \x1b[0m原配置项: ${Object.keys(originalAdditives).length}个`);
  console.log(`  \x1b[36m[对比] \x1b[0m新配置项: ${Object.keys(additionalFees).length}个`);
  
  // 显示新配置
  console.log(`  \x1b[36m[新配置] \x1b[0m附加费清单:`);
  Object.entries(additionalFees).forEach(([name, amount]) => {
    console.log(`    ${name}: ${amount}元/立方米`);
  });
}

/**
 * 从Excel文件路径读取附加费数据并更新配置
 * @param {string} excelFilePath - Excel文件路径
 * @returns {Promise<Object>} 附加费数据对象
 */
async function loadAdditionalFeeFromFile(excelFilePath) {
  try {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile(excelFilePath);
    
    const additionalFees = readAdditionalFeeData(workbook);
    updateAdditionalFeeConfig(additionalFees);
    
    return additionalFees;
  } catch (error) {
    console.error(`\x1b[31m[错误] \x1b[0m从文件加载附加费数据失败: ${error.message}`);
    return BusinessRules.concretePricing.additives;
  }
}

/**
 * 从已加载的工作簿读取附加费数据并更新配置
 * @param {ExcelJS.Workbook} workbook - 已加载的Excel工作簿
 * @returns {Object} 附加费数据对象
 */
function loadAdditionalFeeFromWorkbook(workbook) {
  const additionalFees = readAdditionalFeeData(workbook);
  updateAdditionalFeeConfig(additionalFees);
  return additionalFees;
}

module.exports = {
  readAdditionalFeeData,
  updateAdditionalFeeConfig,
  loadAdditionalFeeFromFile,
  loadAdditionalFeeFromWorkbook,
};
