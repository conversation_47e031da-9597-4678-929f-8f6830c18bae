const { BusinessRules } = require("./config");
const { CostType } = require("./types");

/**
 * Excel公式生成引擎
 * 统一管理所有Excel公式的生成逻辑
 */

/**
 * 公式类型枚举
 */
const FormulaType = {
  // 基础计算公式
  SUM: "SUM",
  MULTIPLY: "MULTIPLY",
  DIVIDE: "DIVIDE",
  
  // 查找引用公式
  VLOOKUP: "VLOOKUP",
  INDEX_MATCH: "INDEX_MATCH",
  IFERROR: "IFERROR",
  
  // 条件公式
  IF: "IF",
  SUMIF: "SUMIF",
  COUNTIF: "COUNTIF",
  
  // 跨表引用
  CROSS_SHEET: "CROSS_SHEET"
};

/**
 * Excel公式生成器类
 */
class FormulaEngine {
  constructor() {
    this.sheetNames = BusinessRules.outputFormat.sheets;
    this.columns = BusinessRules.outputFormat.columns;
  }

  /**
   * 生成基础算术公式
   * @param {string} type - 公式类型
   * @param {Array} cells - 单元格数组
   * @returns {string} Excel公式
   */
  generateBasicFormula(type, cells) {
    switch (type) {
      case FormulaType.SUM:
        if (cells.length === 1 && cells[0].includes(':')) {
          return `=SUM(${cells[0]})`;
        }
        return `=${cells.join('+')}`;
      
      case FormulaType.MULTIPLY:
        return `=${cells.join('*')}`;
      
      case FormulaType.DIVIDE:
        return `=${cells[0]}/${cells[1]}`;
      
      default:
        return "0";
    }
  }

  /**
   * 生成查找公式
   * @param {string} lookupValue - 查找值
   * @param {string} tableArray - 查找范围
   * @param {number} colIndex - 列索引
   * @param {boolean} exactMatch - 是否精确匹配
   * @returns {string} Excel公式
   */
  generateLookupFormula(lookupValue, tableArray, colIndex, exactMatch = true) {
    const matchType = exactMatch ? "FALSE" : "TRUE";
    return `=VLOOKUP("${lookupValue}",${tableArray},${colIndex},${matchType})`;
  }

  /**
   * 生成INDEX+MATCH公式（更灵活的查找）
   * @param {string} returnColumn - 返回值列
   * @param {string} lookupColumn - 查找列
   * @param {string} lookupValue - 查找值
   * @returns {string} Excel公式
   */
  generateIndexMatchFormula(returnColumn, lookupColumn, lookupValue) {
    return `=INDEX(${returnColumn},MATCH("${lookupValue}",${lookupColumn},0))`;
  }

  /**
   * 生成带错误处理的公式
   * @param {string} formula - 主公式
   * @param {string} errorValue - 错误时返回值
   * @returns {string} Excel公式
   */
  generateIferrorFormula(formula, errorValue = "0") {
    // 移除公式开头的=号（如果有）
    const cleanFormula = formula.startsWith('=') ? formula.substring(1) : formula;
    return `=IFERROR(${cleanFormula},${errorValue})`;
  }

  /**
   * 生成条件求和公式
   * @param {string} range - 求和范围
   * @param {string} criteria - 条件
   * @param {string} sumRange - 求和列（可选）
   * @returns {string} Excel公式
   */
  generateSumifFormula(range, criteria, sumRange = null) {
    if (sumRange) {
      return `=SUMIF(${range},"${criteria}",${sumRange})`;
    }
    return `=SUMIF(${range},"${criteria}")`;
  }

  /**
   * 生成跨工作表引用公式
   * @param {string} sheetName - 工作表名称
   * @param {string} cellRange - 单元格范围
   * @returns {string} Excel公式
   */
  generateCrossSheetReference(sheetName, cellRange) {
    return `='${sheetName}'.${cellRange}`;
  }

  /**
   * 生成测算成本量公式（H列）
   * @param {Object} currentItem - 当前项目
   * @param {Object} parentTarget - 父级目标项
   * @returns {string} Excel公式
   */
  generateCalculatedQuantityFormula(currentItem, parentTarget) {
    if (!parentTarget) {
      return `=F${currentItem.rowIndex}`;
    }
    return this.generateBasicFormula(FormulaType.MULTIPLY, [
      `F${parentTarget.rowIndex}`,
      `G${currentItem.rowIndex}`
    ]);
  }

  /**
   * 生成跨表单价引用公式（I列）
   * @param {Object} currentItem - 当前项目
   * @returns {string} Excel公式
   */
  generateUnitPriceFormula(currentItem) {
    const itemName = currentItem.itemName;
    let sheetName = "";
    
    switch (currentItem.costType) {
      case CostType.MATERIAL:
        sheetName = this.sheetNames.materialList;
        break;
      case CostType.LABOR:
        sheetName = this.sheetNames.laborList;
        break;
      case CostType.SUBCONTRACT:
        sheetName = this.sheetNames.subcontractList;
        break;
      default:
        return "0";
    }
    
    const lookupFormula = this.generateIndexMatchFormula(
      `'${sheetName}'.H:H`,
      `'${sheetName}'.C:C`,
      itemName
    );
    
    return this.generateIferrorFormula(lookupFormula, "0");
  }

  /**
   * 生成成本合计公式（J列）
   * @param {number} rowIndex - 行索引
   * @returns {string} Excel公式
   */
  generateTotalCostFormula(rowIndex) {
    return this.generateBasicFormula(FormulaType.SUM, [
      `K${rowIndex}`,
      `L${rowIndex}`,
      `M${rowIndex}`
    ]);
  }

  /**
   * 生成成本分配公式（K/L/M列）
   * @param {Object} currentItem - 当前项目
   * @param {number} rowIndex - 行索引
   * @returns {Object} 包含K/L/M列公式的对象
   */
  generateCostAllocationFormulas(currentItem, rowIndex) {
    const amountFormula = this.generateBasicFormula(FormulaType.MULTIPLY, [
      `H${rowIndex}`,
      `I${rowIndex}`
    ]);
    
    let formulas = {
      laborCost: "0",
      materialCost: "0",
      subcontractCost: "0"
    };
    
    switch (currentItem.costType) {
      case CostType.LABOR:
        formulas.laborCost = amountFormula;
        break;
      case CostType.MATERIAL:
        formulas.materialCost = amountFormula;
        break;
      case CostType.SUBCONTRACT:
        formulas.subcontractCost = amountFormula;
        break;
      case CostType.TARGET:
        // 目标项汇总所有下属成本
        formulas.laborCost = this.generateSumifFormula("B:B", "人", "K:K");
        formulas.materialCost = this.generateSumifFormula("B:B", "材", "L:L");
        formulas.subcontractCost = this.generateSumifFormula("B:B", "专", "M:M");
        break;
    }
    
    return formulas;
  }

  /**
   * 生成综合单价公式（N列）
   * @param {Object} currentItem - 当前项目
   * @param {number} rowIndex - 行索引
   * @returns {string} Excel公式
   */
  generateComprehensiveUnitPriceFormula(currentItem, rowIndex) {
    if (currentItem.costType === CostType.TARGET) {
      return `=IF(F${rowIndex}=0,0,J${rowIndex}/F${rowIndex})`;
    }
    return "0";
  }

  /**
   * 生成进项税额公式（P列）
   * @param {Object} currentItem - 当前项目
   * @param {number} rowIndex - 行索引
   * @returns {string} Excel公式
   */
  generateTaxAmountFormula(currentItem, rowIndex) {
    switch (currentItem.costType) {
      case CostType.MATERIAL:
        return `=L${rowIndex}*O${rowIndex}/(1+O${rowIndex})`;
      case CostType.LABOR:
        return `=K${rowIndex}*O${rowIndex}/(1+O${rowIndex})`;
      case CostType.SUBCONTRACT:
        return `=M${rowIndex}*O${rowIndex}/(1+O${rowIndex})`;
      case CostType.TARGET:
        return `=K${rowIndex}*0.03/(1+0.03)+L${rowIndex}*0.13/(1+0.13)+M${rowIndex}*0.09/(1+0.09)`;
      default:
        return "0";
    }
  }

  /**
   * 生成清单项金额公式
   * @param {number} rowIndex - 行索引
   * @returns {string} Excel公式
   */
  generateListAmountFormula(rowIndex) {
    return this.generateBasicFormula(FormulaType.MULTIPLY, [
      `G${rowIndex}`,
      `H${rowIndex}`
    ]);
  }

  /**
   * 生成分部合计公式
   * @param {Array} childRows - 子项行号数组
   * @param {string} column - 列名
   * @returns {string} Excel公式
   */
  generateDivisionTotalFormula(childRows, column = "I") {
    if (childRows.length === 0) return "0";
    
    const range = `${column}${Math.min(...childRows)}:${column}${Math.max(...childRows)}`;
    return this.generateBasicFormula(FormulaType.SUM, [range]);
  }

  /**
   * 生成名称合并公式
   * @param {Array} childRows - 子项行号数组
   * @param {string} column - 列名
   * @returns {string} Excel公式
   */
  generateNameMergedFormula(childRows, column = "I") {
    if (childRows.length <= 1) return "0";
    
    const cellRefs = childRows.map(row => `${column}${row}`);
    return this.generateBasicFormula(FormulaType.SUM, cellRefs);
  }

  /**
   * 验证公式语法
   * @param {string} formula - Excel公式
   * @returns {Object} 验证结果
   */
  validateFormula(formula) {
    const validation = {
      isValid: true,
      errors: [],
      warnings: []
    };
    
    // 基础语法检查
    if (!formula.startsWith('=') && formula !== "0") {
      validation.errors.push("公式必须以=开头");
      validation.isValid = false;
    }
    
    // 括号匹配检查
    const openBrackets = (formula.match(/\(/g) || []).length;
    const closeBrackets = (formula.match(/\)/g) || []).length;
    if (openBrackets !== closeBrackets) {
      validation.errors.push("括号不匹配");
      validation.isValid = false;
    }
    
    // 引用检查
    const sheetRefs = formula.match(/'[^']+'\./g);
    if (sheetRefs) {
      sheetRefs.forEach(ref => {
        const sheetName = ref.replace(/'/g, '').replace('.', '');
        const validSheets = Object.values(this.sheetNames);
        if (!validSheets.includes(sheetName)) {
          validation.warnings.push(`工作表引用可能无效: ${sheetName}`);
        }
      });
    }
    
    return validation;
  }
}

// 创建全局实例
const formulaEngine = new FormulaEngine();

module.exports = {
  FormulaType,
  FormulaEngine,
  formulaEngine
};
