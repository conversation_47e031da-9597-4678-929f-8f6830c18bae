const ExcelJS = require("exceljs");
const path = require("path");
const fs = require("fs");
const { BusinessRules } = require("./config");
const { formulaEngine } = require("./formula-engine");

/**
 * Excel输出引擎
 * 实现Excel文件输出，支持公式写入和格式设置
 */

/**
 * Excel样式配置
 */
const ExcelStyles = {
  // 表头样式
  header: {
    font: { bold: true, size: 11, name: "宋体" },
    alignment: { horizontal: "center", vertical: "middle" },
    fill: { type: "pattern", pattern: "solid", fgColor: { argb: "FFE6E6FA" } },
    border: {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    },
  },

  // 数据样式
  data: {
    font: { size: 10, name: "宋体" },
    alignment: { horizontal: "left", vertical: "middle" },
    border: {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    },
  },

  // 数值样式
  number: {
    font: { size: 10, name: "宋体" },
    alignment: { horizontal: "right", vertical: "middle" },
    numFmt: "#,##0.00",
    border: {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    },
  },

  // 分部合计样式
  divisionTotal: {
    font: { bold: true, size: 10, name: "宋体" },
    alignment: { horizontal: "left", vertical: "middle" },
    fill: { type: "pattern", pattern: "solid", fgColor: { argb: "FFF0F8FF" } },
    border: {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    },
  },

  // 合并项样式
  merged: {
    font: { size: 10, name: "宋体" },
    alignment: { horizontal: "left", vertical: "middle" },
    fill: { type: "pattern", pattern: "solid", fgColor: { argb: "FFF5F5F5" } },
    border: {
      top: { style: "thin" },
      left: { style: "thin" },
      bottom: { style: "thin" },
      right: { style: "thin" },
    },
  },
};

/**
 * Excel输出器类
 */
class ExcelWriter {
  constructor() {
    this.workbook = null;
    this.sheetNames = BusinessRules.outputFormat.sheets;
    this.headers = BusinessRules.outputFormat.headers;
  }

  /**
   * 创建新的工作簿
   */
  createWorkbook() {
    this.workbook = new ExcelJS.Workbook();
    this.workbook.creator = "清单测算工具";
    this.workbook.created = new Date();
    this.workbook.modified = new Date();
  }

  /**
   * 更新分部分项成本测算工作表
   * @param {Object} worksheet - 原始工作表
   * @param {Array} calculatedData - 计算结果数据
   */
  updateCostCalculationSheet(worksheet, calculatedData) {
    console.log(
      `  \x1b[36m[更新] \x1b[0m更新分部分项成本测算表，共 \x1b[32m${calculatedData.length}\x1b[0m 个项目`
    );

    calculatedData.forEach((item) => {
      const row = worksheet.getRow(item.rowIndex);

      // H列：测算成本量
      const hCell = row.getCell(8);
      const hFormula = item.getFormula("calculatedQuantity");
      if (hFormula) {
        hCell.value = { formula: hFormula };
      }
      hCell.style = ExcelStyles.number;

      // I列：测算单价
      const iCell = row.getCell(9);
      const iFormula = item.getFormula("unitPrice");
      if (iFormula) {
        iCell.value = { formula: iFormula };
      }
      iCell.style = ExcelStyles.number;

      // J列：测算成本合计
      const jCell = row.getCell(10);
      const jFormula = item.getFormula("totalCost");
      if (jFormula) {
        jCell.value = { formula: jFormula };
      }
      jCell.style = ExcelStyles.number;

      // K列：劳务费
      const kCell = row.getCell(11);
      const kFormula = item.getFormula("laborCost");
      if (kFormula) {
        kCell.value = { formula: kFormula };
      }
      kCell.style = ExcelStyles.number;

      // L列：材料费
      const lCell = row.getCell(12);
      const lFormula = item.getFormula("materialCost");
      if (lFormula) {
        lCell.value = { formula: lFormula };
      }
      lCell.style = ExcelStyles.number;

      // M列：专业分包
      const mCell = row.getCell(13);
      const mFormula = item.getFormula("subcontractCost");
      if (mFormula) {
        mCell.value = { formula: mFormula };
      }
      mCell.style = ExcelStyles.number;

      // N列：测算综合单价
      const nCell = row.getCell(14);
      const nFormula = item.getFormula("comprehensiveUnitPrice");
      if (nFormula) {
        nCell.value = { formula: nFormula };
      }
      nCell.style = ExcelStyles.number;

      // O列：税率
      const oCell = row.getCell(15);
      oCell.value = item.taxRate;
      oCell.style = ExcelStyles.number;
      oCell.numFmt = "0.00%";

      // P列：进项税额
      const pCell = row.getCell(16);
      const pFormula = item.getFormula("taxAmount");
      if (pFormula) {
        pCell.value = { formula: pFormula };
      }
      pCell.style = ExcelStyles.number;

      // Q列：备注
      const qCell = row.getCell(17);
      qCell.value = item.remarks || "";
      qCell.style = ExcelStyles.data;
    });
  }

  /**
   * 创建材料清单工作表
   * @param {Array} materialList - 材料清单数据
   */
  createMaterialListSheet(materialList) {
    console.log(
      `  \x1b[36m[创建] \x1b[0m创建材料清单工作表，共 \x1b[32m${materialList.length}\x1b[0m 个项目`
    );

    const worksheet = this.workbook.addWorksheet(this.sheetNames.materialList);

    // 设置列宽
    worksheet.columns = [
      { width: 8 }, // A: 序号
      { width: 15 }, // B: 分部工程
      { width: 25 }, // C: 原测算清单
      { width: 30 }, // D: 规格型号
      { width: 10 }, // E: 计量单位
      { width: 12 }, // F: 测算含量
      { width: 12 }, // G: 测算成本量
      { width: 12 }, // H: 测算单价
      { width: 12 }, // I: 测算金额
      { width: 10 }, // J: 税率
    ];

    // 添加表头
    const headerRow = worksheet.getRow(1);
    this.headers.materialList.forEach((header, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.value = header;
      cell.style = ExcelStyles.header;
    });

    // 添加数据
    let currentRow = 2;
    materialList.forEach((item) => {
      const row = worksheet.getRow(currentRow);

      // 根据项目类型设置样式
      let style = ExcelStyles.data;
      if (item.isParent) {
        style = ExcelStyles.divisionTotal;
      } else if (item.type === "merged") {
        style = ExcelStyles.merged;
      }

      // A列：序号
      row.getCell(1).value = item.sortOrder || "";
      row.getCell(1).style = style;

      // B列：分部工程
      row.getCell(2).value = item.division;
      row.getCell(2).style = style;

      // C列：原测算清单
      row.getCell(3).value = item.originalItem;
      row.getCell(3).style = style;

      // D列：规格型号
      row.getCell(4).value = item.specification;
      row.getCell(4).style = style;

      // E列：计量单位
      row.getCell(5).value = item.unit;
      row.getCell(5).style = style;

      // F列：测算含量
      const fCell = row.getCell(6);
      const fFormula = item.getFormula ? item.getFormula("content") : null;
      if (fFormula) {
        fCell.value = { formula: fFormula };
      } else {
        fCell.value = item.content;
      }
      fCell.style = { ...ExcelStyles.number, ...style };

      // G列：测算成本量
      const gCell = row.getCell(7);
      const gFormula = item.getFormula ? item.getFormula("quantity") : null;
      if (gFormula) {
        gCell.value = { formula: gFormula };
      } else {
        gCell.value = item.quantity;
      }
      gCell.style = { ...ExcelStyles.number, ...style };

      // H列：测算单价
      row.getCell(8).value = item.unitPrice;
      row.getCell(8).style = { ...ExcelStyles.number, ...style };

      // I列：测算金额
      const iCell = row.getCell(9);
      const iFormula = item.getFormula ? item.getFormula("amount") : null;
      if (iFormula) {
        iCell.value = { formula: iFormula };
      } else {
        iCell.value = item.amount;
      }
      iCell.style = { ...ExcelStyles.number, ...style };

      // J列：税率
      row.getCell(10).value = item.taxRate;
      row.getCell(10).style = { ...ExcelStyles.number, ...style };
      row.getCell(10).numFmt = "0.00%";

      currentRow++;
    });

    return worksheet;
  }

  /**
   * 创建劳务清单工作表
   * @param {Array} laborList - 劳务清单数据
   */
  createLaborListSheet(laborList) {
    console.log(
      `  \x1b[36m[创建] \x1b[0m创建劳务清单工作表，共 \x1b[32m${laborList.length}\x1b[0m 个项目`
    );

    const worksheet = this.workbook.addWorksheet(this.sheetNames.laborList);

    // 设置列宽
    worksheet.columns = [
      { width: 8 }, // A: 序号
      { width: 15 }, // B: 分部工程
      { width: 25 }, // C: 原测算清单
      { width: 30 }, // D: 工作内容
      { width: 10 }, // E: 计量单位
      { width: 12 }, // F: 测算含量
      { width: 12 }, // G: 测算成本量
      { width: 12 }, // H: 测算单价
      { width: 12 }, // I: 测算金额
      { width: 10 }, // J: 税率
    ];

    // 添加表头
    const headerRow = worksheet.getRow(1);
    this.headers.laborList.forEach((header, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.value = header;
      cell.style = ExcelStyles.header;
    });

    // 添加数据
    this.writeListData(worksheet, laborList, 2);

    return worksheet;
  }

  /**
   * 创建专业分包清单工作表
   * @param {Array} subcontractList - 专业分包清单数据
   */
  createSubcontractListSheet(subcontractList) {
    console.log(
      `  \x1b[36m[创建] \x1b[0m创建专业分包清单工作表，共 \x1b[32m${subcontractList.length}\x1b[0m 个项目`
    );

    const worksheet = this.workbook.addWorksheet(
      this.sheetNames.subcontractList
    );

    // 设置列宽（与劳务清单相同）
    worksheet.columns = [
      { width: 8 }, // A: 序号
      { width: 15 }, // B: 分部工程
      { width: 25 }, // C: 原测算清单
      { width: 30 }, // D: 工作内容
      { width: 10 }, // E: 计量单位
      { width: 12 }, // F: 测算含量
      { width: 12 }, // G: 测算成本量
      { width: 12 }, // H: 测算单价
      { width: 12 }, // I: 测算金额
      { width: 10 }, // J: 税率
    ];

    // 添加表头
    const headerRow = worksheet.getRow(1);
    this.headers.subcontractList.forEach((header, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.value = header;
      cell.style = ExcelStyles.header;
    });

    // 添加数据
    this.writeListData(worksheet, subcontractList, 2);

    return worksheet;
  }

  /**
   * 写入清单数据（劳务和专业分包通用）
   * @param {Object} worksheet - 工作表对象
   * @param {Array} listData - 清单数据
   * @param {number} startRow - 起始行号
   */
  writeListData(worksheet, listData, startRow) {
    let currentRow = startRow;

    listData.forEach((item) => {
      const row = worksheet.getRow(currentRow);

      // 根据项目类型设置样式
      let style = ExcelStyles.data;
      if (item.type === "division") {
        style = ExcelStyles.divisionTotal;
      } else if (item.type === "merged") {
        style = ExcelStyles.merged;
      }

      // A列：序号
      row.getCell(1).value = item.serialNo || "";
      row.getCell(1).style = style;

      // B列：分部工程
      row.getCell(2).value = item.division;
      row.getCell(2).style = style;

      // C列：原测算清单
      row.getCell(3).value = item.originalItem;
      row.getCell(3).style = style;

      // D列：工作内容
      row.getCell(4).value = item.workContent;
      row.getCell(4).style = style;

      // E列：计量单位
      row.getCell(5).value = item.unit;
      row.getCell(5).style = style;

      // F列：测算含量
      const fCell = row.getCell(6);
      const fFormula = item.getFormula ? item.getFormula("content") : null;
      if (fFormula) {
        fCell.value = { formula: fFormula };
      } else {
        fCell.value = item.content;
      }
      fCell.style = { ...ExcelStyles.number, ...style };

      // G列：测算成本量
      const gCell = row.getCell(7);
      const gFormula = item.getFormula ? item.getFormula("quantity") : null;
      if (gFormula) {
        gCell.value = { formula: gFormula };
      } else {
        gCell.value = item.quantity;
      }
      gCell.style = { ...ExcelStyles.number, ...style };

      // H列：测算单价
      row.getCell(8).value = item.unitPrice;
      row.getCell(8).style = { ...ExcelStyles.number, ...style };

      // I列：测算金额
      const iCell = row.getCell(9);
      const iFormula = item.getFormula ? item.getFormula("amount") : null;
      if (iFormula) {
        iCell.value = { formula: iFormula };
      } else {
        iCell.value = item.amount;
      }
      iCell.style = { ...ExcelStyles.number, ...style };

      // J列：税率
      row.getCell(10).value = item.taxRate;
      row.getCell(10).style = { ...ExcelStyles.number, ...style };
      row.getCell(10).numFmt = "0.00%";

      currentRow++;
    });
  }

  /**
   * 保存Excel文件
   * @param {string} outputPath - 输出路径
   */
  async saveWorkbook(outputPath) {
    try {
      // 确保输出目录存在
      const outputDir = path.dirname(outputPath);
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      await this.workbook.xlsx.writeFile(outputPath);
      console.log(
        `  \x1b[32m[保存] \x1b[0m文件已保存: \x1b[32m${outputPath}\x1b[0m`
      );
      return true;
    } catch (error) {
      console.error(`  \x1b[31m[错误] \x1b[0m保存文件失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 生成完整的Excel输出文件
   * @param {Object} inputData - 输入数据
   * @param {Object} results - 处理结果
   * @param {string} outputPath - 输出路径
   * @returns {Promise<Object>} 输出结果
   */
  async generateExcelOutput(inputData, results, outputPath) {
    console.log(`\n\x1b[36m[Excel输出] \x1b[0m开始生成Excel文件`);

    try {
      // 1. 创建新工作簿
      this.createWorkbook();

      // 2. 复制原始工作表并更新
      if (inputData.worksheet) {
        const costSheet = this.workbook.addWorksheet(
          this.sheetNames.costCalculation
        );

        // 复制原始数据
        inputData.worksheet.eachRow((row, rowNumber) => {
          const newRow = costSheet.getRow(rowNumber);
          row.eachCell((cell, colNumber) => {
            if (colNumber <= 7) {
              // 只复制A~G列的原始数据
              newRow.getCell(colNumber).value = cell.value;
              newRow.getCell(colNumber).style = cell.style;
            }
          });
        });

        // 更新H~Q列
        this.updateCostCalculationSheet(costSheet, results.calculatedData);
      }

      // 3. 创建材料清单工作表
      if (results.materialList && results.materialList.length > 0) {
        this.createMaterialListSheet(results.materialList);
      }

      // 4. 创建劳务清单工作表
      if (results.laborList && results.laborList.length > 0) {
        this.createLaborListSheet(results.laborList);
      }

      // 5. 创建专业分包清单工作表
      if (results.subcontractList && results.subcontractList.length > 0) {
        this.createSubcontractListSheet(results.subcontractList);
      }

      // 6. 保存文件
      const saved = await this.saveWorkbook(outputPath);

      if (saved) {
        console.log(
          `\x1b[36m[Excel输出] \x1b[0m文件生成完成: \x1b[32m${path.basename(
            outputPath
          )}\x1b[0m`
        );
        return {
          success: true,
          outputPath,
          worksheetCount: this.workbook.worksheets.length,
        };
      } else {
        throw new Error("文件保存失败");
      }
    } catch (error) {
      console.error(`\x1b[31m[错误] \x1b[0mExcel输出失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        outputPath: "",
      };
    }
  }
}

/**
 * 主要的Excel输出函数
 * @param {Object} inputData - 输入数据
 * @param {Object} allResults - 所有处理结果
 * @param {string} outputPath - 输出路径
 * @returns {Promise<Object>} 输出结果
 */
async function generateExcelOutput(inputData, allResults, outputPath) {
  const writer = new ExcelWriter();
  return await writer.generateExcelOutput(inputData, allResults, outputPath);
}

module.exports = {
  ExcelStyles,
  ExcelWriter,
  generateExcelOutput,
};
