const ExcelJS = require('exceljs');

async function checkAdditionalFeeDetails() {
  try {
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.readFile('input/bill-calculation/工具2-人材专业分包工具+规则.xlsx');
    
    const additionalFeeSheet = workbook.getWorksheet('4-附加费');
    
    if (!additionalFeeSheet) {
      console.log('❌ 未找到4-附加费页签');
      return;
    }
    
    console.log('📋 4-附加费页签详细信息:');
    console.log('='.repeat(50));
    
    // 查看表头
    console.log('表头信息:');
    const headers = [];
    if (additionalFeeSheet.getRow(1)) {
      additionalFeeSheet.getRow(1).eachCell((cell, colNumber) => {
        if (cell.value) {
          headers.push(`${String.fromCharCode(64 + colNumber)}列: ${cell.value}`);
        }
      });
      console.log(headers.join('\n'));
    }
    
    console.log('\n详细数据:');
    console.log('-'.repeat(50));
    
    // 查看所有数据行
    for (let i = 1; i <= additionalFeeSheet.rowCount; i++) {
      const row = additionalFeeSheet.getRow(i);
      const rowData = [];
      
      row.eachCell((cell, colNumber) => {
        let value = '';
        if (cell.value !== null && cell.value !== undefined) {
          if (typeof cell.value === 'object' && cell.value.richText) {
            // 处理富文本
            value = cell.value.richText.map(rt => rt.text).join('');
          } else if (typeof cell.value === 'object' && cell.value.formula) {
            // 处理公式
            value = `公式: ${cell.value.formula}`;
          } else {
            value = cell.value.toString();
          }
        }
        rowData.push(value);
      });
      
      if (rowData.some(data => data.trim() !== '')) {
        console.log(`第${i}行: ${rowData.map((data, index) => `${String.fromCharCode(65 + index)}="${data}"`).join(' | ')}`);
      }
    }
    
  } catch (error) {
    console.error('错误:', error.message);
  }
}

checkAdditionalFeeDetails();
