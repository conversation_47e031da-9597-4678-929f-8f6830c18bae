const ExcelJS = require("exceljs");

async function compareSheetDetails() {
  try {
    // 读取需求文档
    const reqWorkbook = new ExcelJS.Workbook();
    await reqWorkbook.xlsx.readFile(
      "input/bill-calculation/工具2-人材专业分包工具+规则.xlsx"
    );

    // 读取输出文件
    const outputWorkbook = new ExcelJS.Workbook();
    await outputWorkbook.xlsx.readFile(
      "output/bill-calculation/工具2-人材专业分包工具+规则_测算结果_2025-06-20T10-22-31.xlsx"
    );

    console.log("详细对比分析:");
    console.log("=".repeat(50));

    const targetSheets = [
      "3-分部分项成本测算",
      "4-材料清单",
      "4-劳务清单",
      "4-专业分包清单",
    ];

    targetSheets.forEach((sheetName) => {
      console.log(`\n📋 页签: ${sheetName}`);

      const reqSheet = reqWorkbook.getWorksheet(sheetName);
      const outputSheet = outputWorkbook.getWorksheet(sheetName);

      if (!reqSheet) {
        console.log("  ❌ 需求文档中不存在此页签");
        return;
      }

      if (!outputSheet) {
        console.log("  ❌ 输出文件中不存在此页签");
        return;
      }

      console.log("  ✅ 两个文件中都存在此页签");

      // 检查表头
      console.log("  📊 表头对比:");
      const reqHeaders = [];
      const outputHeaders = [];

      if (reqSheet.getRow(1)) {
        reqSheet.getRow(1).eachCell((cell, colNumber) => {
          if (cell.value) reqHeaders.push(cell.value.toString());
        });
      }

      if (outputSheet.getRow(1)) {
        outputSheet.getRow(1).eachCell((cell, colNumber) => {
          if (cell.value) outputHeaders.push(cell.value.toString());
        });
      }

      console.log(
        `    需求文档表头 (${reqHeaders.length}列): ${reqHeaders.join(", ")}`
      );
      console.log(
        `    输出文件表头 (${outputHeaders.length}列): ${outputHeaders.join(
          ", "
        )}`
      );

      // 检查列数是否匹配
      if (reqHeaders.length !== outputHeaders.length) {
        console.log(
          `    ⚠️  列数不匹配: 需求${reqHeaders.length}列 vs 输出${outputHeaders.length}列`
        );
      }

      // 检查表头内容是否匹配
      const headerMismatch = reqHeaders.some(
        (header, index) => header !== outputHeaders[index]
      );
      if (headerMismatch) {
        console.log("    ⚠️  表头内容不匹配");
        reqHeaders.forEach((header, index) => {
          if (header !== outputHeaders[index]) {
            console.log(
              `      第${index + 1}列: 需求'${header}' vs 输出'${
                outputHeaders[index] || "(空)"
              }'`
            );
          }
        });
      } else {
        console.log("    ✅ 表头内容匹配");
      }
    });
  } catch (error) {
    console.error("错误:", error.message);
  }
}

compareSheetDetails();
